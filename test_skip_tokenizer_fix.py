#!/usr/bin/env python3
"""
Test script to verify that the skip_tokenizer_init=True fix works correctly.
This reproduces the issue from GitHub issue #21846.
"""

import vllm
from transformers import AutoTokenizer

def test_skip_tokenizer_init():
    """Test that skip_tokenizer_init=True works without AttributeError."""
    print("Testing skip_tokenizer_init=True fix...")
    
    model_name = "Qwen/Qwen2.5-1.5B"
    
    try:
        # This should work without throwing AttributeError
        print(f"Creating LLM with model: {model_name}")
        llm = vllm.LLM(model_name, skip_tokenizer_init=True)
        
        print("Loading external tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        print("Encoding test prompt...")
        input_ids = tokenizer.encode("test")
        
        print("Generating with prompt_token_ids...")
        result = llm.generate([{"prompt_token_ids": input_ids}])
        
        print("SUCCESS: Generation completed without errors!")
        print(f"Generated {len(result)} outputs")
        
        # Print the first result if available
        if result and len(result) > 0:
            output = result[0]
            print(f"Output token IDs: {output.outputs[0].token_ids}")
        
        return True
        
    except AttributeError as e:
        if "'NoneType' object has no attribute 'get_lora_tokenizer'" in str(e):
            print(f"FAILED: The original bug still exists: {e}")
            return False
        else:
            print(f"FAILED: Unexpected AttributeError: {e}")
            return False
    except Exception as e:
        print(f"FAILED: Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_skip_tokenizer_init()
    exit(0 if success else 1)
