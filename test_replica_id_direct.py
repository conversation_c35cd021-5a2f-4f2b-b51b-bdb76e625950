#!/usr/bin/env python3
"""
Direct test of ReplicaId functionality without full vLLM dependencies.
"""

import sys
import os

def test_get_replica_id_functions():
    """Test the _get_replica_id functions directly."""
    print("Testing _get_replica_id functions...")
    
    # Test v1 implementation by importing the module directly
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'vllm', 'v1', 'metrics'))
        import ray_wrappers
        replica_id = ray_wrappers._get_replica_id()
        print(f"✓ V1 _get_replica_id works: {replica_id}")
        assert replica_id.startswith("replica-"), f"Expected replica- prefix, got: {replica_id}"
        sys.path.pop(0)
    except Exception as e:
        print(f"✗ V1 _get_replica_id failed: {e}")
        if len(sys.path) > 0 and 'ray_wrappers' in sys.path[0]:
            sys.path.pop(0)
    
    # Test legacy implementation by importing the module directly
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'vllm', 'engine'))
        import metrics
        replica_id = metrics._get_replica_id()
        print(f"✓ Legacy _get_replica_id works: {replica_id}")
        assert replica_id.startswith("replica-"), f"Expected replica- prefix, got: {replica_id}"
        sys.path.pop(0)
    except Exception as e:
        print(f"✗ Legacy _get_replica_id failed: {e}")
        if len(sys.path) > 0 and 'metrics' in sys.path[0]:
            sys.path.pop(0)

def test_ray_metrics_with_replica_id():
    """Test Ray metrics with ReplicaId using our simple approach."""
    print("\nTesting Ray metrics with ReplicaId...")
    
    try:
        import ray
        from ray.util import metrics as ray_metrics
        print("✓ Ray is available")
        
        # Initialize Ray
        if not ray.is_initialized():
            try:
                ray.shutdown()
            except:
                pass
            ray.init(local_mode=True, ignore_reinit_error=True)
            print("✓ Ray initialized")
        
        # Create a simple ReplicaId function
        def get_replica_id():
            try:
                runtime_context = ray.get_runtime_context()
                worker_id = runtime_context.get_worker_id()
                if worker_id:
                    return f"replica-{worker_id[-8:]}"
            except Exception:
                pass
            return "replica-unknown"
        
        replica_id = get_replica_id()
        print(f"✓ ReplicaId generated: {replica_id}")
        
        # Test creating Ray metrics with ReplicaId tag
        gauge = ray_metrics.Gauge("test_gauge", "Test gauge", tag_keys=("ReplicaId",))
        gauge.set_default_tags({"ReplicaId": replica_id})
        gauge.set(42)
        print("✓ Ray Gauge with ReplicaId created and set")
        
        counter = ray_metrics.Counter("test_counter", "Test counter", tag_keys=("ReplicaId",))
        counter.set_default_tags({"ReplicaId": replica_id})
        counter.inc(1)
        print("✓ Ray Counter with ReplicaId created and incremented")
        
        histogram = ray_metrics.Histogram("test_histogram", "Test histogram", 
                                         boundaries=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0], 
                                         tag_keys=("ReplicaId",))
        histogram.set_default_tags({"ReplicaId": replica_id})
        histogram.observe(1.5)
        print("✓ Ray Histogram with ReplicaId created and observed")
        
        # Verify default tags are set
        if hasattr(gauge, '_default_tags') and 'ReplicaId' in gauge._default_tags:
            print(f"✓ Gauge has ReplicaId in default tags: {gauge._default_tags['ReplicaId']}")
        
        ray.shutdown()
        print("✓ Ray shutdown successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Ray metrics test failed: {e}")
        import traceback
        traceback.print_exc()
        try:
            ray.shutdown()
        except:
            pass
        return False

def test_implementation_approach():
    """Test our implementation approach for adding ReplicaId to tag_keys."""
    print("\nTesting implementation approach...")
    
    # Test the logic for adding ReplicaId to labelnames
    def add_replica_id_to_labelnames(labelnames):
        labelnames_list = list(labelnames) if labelnames else []
        if "ReplicaId" not in labelnames_list:
            labelnames_list.append("ReplicaId")
        return tuple(labelnames_list)
    
    # Test cases
    test_cases = [
        (None, ("ReplicaId",)),
        ([], ("ReplicaId",)),
        (["metric1"], ("metric1", "ReplicaId")),
        (["metric1", "metric2"], ("metric1", "metric2", "ReplicaId")),
        (["metric1", "ReplicaId", "metric2"], ("metric1", "ReplicaId", "metric2")),
    ]
    
    for input_labelnames, expected in test_cases:
        result = add_replica_id_to_labelnames(input_labelnames)
        if result == expected:
            print(f"✓ {input_labelnames} -> {result}")
        else:
            print(f"✗ {input_labelnames} -> {result}, expected {expected}")
    
    print("✓ Implementation approach verified")

if __name__ == "__main__":
    print("Direct ReplicaId Test")
    print("=" * 50)
    
    test_get_replica_id_functions()
    test_ray_metrics_with_replica_id()
    test_implementation_approach()
    
    print("\n" + "=" * 50)
    print("Direct test completed!")
