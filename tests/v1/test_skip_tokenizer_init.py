"""Test for skip_tokenizer_init=True functionality.

This test verifies that the fix for GitHub issue #21846 works correctly.
The issue was that vLLM 0.10.0 broke skip_tokenizer_init=True by calling
get_lora_tokenizer() on a None tokenizer.
"""

import pytest

from vllm.config import ModelConfig, VllmConfig
from vllm.sampling_params import SamplingParams
from vllm.v1.engine.processor import Processor


def test_processor_with_skip_tokenizer_init():
    """Test that Processor handles None tokenizer correctly when skip_tokenizer_init=True."""
    
    # Create a model config with skip_tokenizer_init=True
    model_config = ModelConfig(
        model="test-model",
        skip_tokenizer_init=True,
        max_model_len=2048,
    )
    
    # Create a minimal VllmConfig
    vllm_config = VllmConfig(model_config=model_config)
    
    # Create processor with None tokenizer (simulating skip_tokenizer_init=True)
    processor = Processor(vllm_config=vllm_config, tokenizer=None)
    
    # Test 1: _validate_sampling_params with allowed_token_ids should not crash
    sampling_params = SamplingParams(allowed_token_ids=[1, 2, 3])
    
    # This should not raise an AttributeError anymore
    processor._validate_sampling_params(sampling_params, lora_request=None)
    
    # Test 2: _validate_sampling_params with empty allowed_token_ids should still raise ValueError
    sampling_params_empty = SamplingParams(allowed_token_ids=[])
    
    with pytest.raises(ValueError, match="allowed_token_ids is not None and empty!"):
        processor._validate_sampling_params(sampling_params_empty, lora_request=None)
    
    # Test 3: _validate_sampling_params with None allowed_token_ids should pass
    sampling_params_none = SamplingParams(allowed_token_ids=None)
    processor._validate_sampling_params(sampling_params_none, lora_request=None)


def test_processor_process_inputs_with_skip_tokenizer_init():
    """Test that process_inputs handles None tokenizer correctly."""
    
    # Create a model config with skip_tokenizer_init=True
    model_config = ModelConfig(
        model="test-model", 
        skip_tokenizer_init=True,
        max_model_len=2048,
    )
    
    # Create a minimal VllmConfig
    vllm_config = VllmConfig(model_config=model_config)
    
    # Create processor with None tokenizer
    processor = Processor(vllm_config=vllm_config, tokenizer=None)
    
    # Create sampling params with bad_words (which would trigger update_from_tokenizer)
    sampling_params = SamplingParams(
        bad_words=["bad", "word"],
        max_tokens=10,
    )
    
    # Create a simple prompt with token IDs (as required when skip_tokenizer_init=True)
    prompt_token_ids = [1, 2, 3, 4, 5]  # Simple token sequence
    
    # This should not crash with AttributeError
    # Note: We can't fully test process_inputs without more complex setup,
    # but the key fix is that update_from_tokenizer is skipped when tokenizer is None
    
    # The fix ensures that the following line in process_inputs doesn't crash:
    # if self.tokenizer is not None:
    #     sampling_params.update_from_tokenizer(self.tokenizer.get_lora_tokenizer(lora_request))
    
    # We can at least verify that the processor was created successfully
    assert processor.tokenizer is None
    assert processor.model_config.skip_tokenizer_init is True


if __name__ == "__main__":
    test_processor_with_skip_tokenizer_init()
    test_processor_process_inputs_with_skip_tokenizer_init()
    print("All tests passed!")
