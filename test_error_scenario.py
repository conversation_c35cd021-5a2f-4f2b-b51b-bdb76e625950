#!/usr/bin/env python3
"""
Test script to verify that the original error scenario is fixed.
This simulates the exact error that was reported:
ValueError: Missing value for tag key(s): ReplicaId.
"""

import sys
import os

def test_original_error_scenario():
    """Test the exact scenario that was causing the original error."""
    print("Testing original error scenario...")
    
    try:
        import ray
        from ray.util import metrics as ray_metrics
        print("✓ Ray is available")
        
        # Initialize Ray
        if not ray.is_initialized():
            try:
                ray.shutdown()
            except:
                pass
            ray.init(local_mode=True, ignore_reinit_error=True)
            print("✓ Ray initialized")
        
        def get_replica_id():
            try:
                runtime_context = ray.get_runtime_context()
                worker_id = runtime_context.get_worker_id()
                if worker_id:
                    return f"replica-{worker_id[-8:]}"
            except Exception:
                pass
            return "replica-unknown"
        
        replica_id = get_replica_id()
        print(f"✓ ReplicaId generated: {replica_id}")
        
        # Simulate the exact scenario from the error:
        # 1. Create a gauge with multiple labelnames (like vLLM does)
        # 2. Add ReplicaId to tag_keys
        # 3. Set default tags with ReplicaId
        # 4. Call labels() method (which was causing the error)
        # 5. Call set() method (which was failing)
        
        print("\n--- Simulating vLLM metric creation ---")
        
        # This simulates how vLLM creates metrics with labelnames
        original_labelnames = ["model", "request_type"]  # Example labelnames from vLLM
        
        # Add ReplicaId to labelnames (our implementation)
        labelnames_list = list(original_labelnames)
        if "ReplicaId" not in labelnames_list:
            labelnames_list.append("ReplicaId")
        labelnames_tuple = tuple(labelnames_list)
        
        print(f"✓ Tag keys: {labelnames_tuple}")
        
        # Create the gauge
        gauge = ray_metrics.Gauge("test_vllm_gauge", "Test vLLM gauge", tag_keys=labelnames_tuple)
        
        # Set ReplicaId as default tag (our implementation)
        gauge.set_default_tags({"ReplicaId": replica_id})
        print("✓ Initial ReplicaId default tag set")
        
        # This would fail with the original error: "Missing value for tag key(s): model,request_type"
        # because Ray requires ALL tag keys to have values
        try:
            gauge.set(42)
            print("✗ Unexpected success - this should have failed without our fix")
        except ValueError as e:
            if "Missing value for tag key(s)" in str(e):
                print(f"✓ Expected error without complete tags: {e}")
            else:
                print(f"✗ Unexpected error: {e}")
        
        print("\n--- Testing our fix ---")
        
        # Now test our fix: provide default values for all tag keys
        complete_tags = {"ReplicaId": replica_id}
        for tag_key in labelnames_tuple:
            if tag_key != "ReplicaId":
                complete_tags[tag_key] = ""  # Default empty value
        
        gauge.set_default_tags(complete_tags)
        print(f"✓ Complete default tags set: {complete_tags}")
        
        # This should now work
        gauge.set(42)
        print("✓ Gauge set() works with complete default tags")
        
        print("\n--- Testing labels() method fix ---")
        
        # Test calling labels() with partial labels (common vLLM usage)
        # This simulates: gauge.labels(model="test_model").set(100)
        
        # Our fixed implementation should:
        # 1. Preserve ReplicaId
        # 2. Provide defaults for missing tag keys
        # 3. Update with provided labels
        
        merged_tags = {"ReplicaId": replica_id}
        
        # Provide defaults for all tag keys
        for tag_key in labelnames_tuple:
            if tag_key != "ReplicaId":
                merged_tags[tag_key] = ""
        
        # Update with provided labels
        provided_labels = {"model": "test_model"}
        merged_tags.update(provided_labels)
        
        # Ensure ReplicaId is preserved
        merged_tags["ReplicaId"] = replica_id
        
        gauge.set_default_tags(merged_tags)
        print(f"✓ Labels method simulation: {merged_tags}")
        
        # This should work
        gauge.set(100)
        print("✓ Gauge set() works after labels() simulation")
        
        print("\n--- Testing edge cases ---")
        
        # Test with no original labelnames (ReplicaId only)
        gauge2 = ray_metrics.Gauge("test_simple_gauge", "Simple gauge", tag_keys=("ReplicaId",))
        gauge2.set_default_tags({"ReplicaId": replica_id})
        gauge2.set(200)
        print("✓ Simple gauge (ReplicaId only) works")
        
        # Test with many labelnames
        many_labelnames = ["model", "request_type", "user_id", "endpoint", "ReplicaId"]
        gauge3 = ray_metrics.Gauge("test_complex_gauge", "Complex gauge", tag_keys=tuple(many_labelnames))
        
        complex_tags = {"ReplicaId": replica_id}
        for tag_key in many_labelnames:
            if tag_key != "ReplicaId":
                complex_tags[tag_key] = f"default_{tag_key}"
        
        gauge3.set_default_tags(complex_tags)
        gauge3.set(300)
        print("✓ Complex gauge (many labels) works")
        
        ray.shutdown()
        print("✓ Ray shutdown successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        try:
            ray.shutdown()
        except:
            pass
        return False

if __name__ == "__main__":
    print("Original Error Scenario Test")
    print("=" * 60)
    print("This test simulates the exact error that was reported:")
    print("ValueError: Missing value for tag key(s): ReplicaId.")
    print("=" * 60)
    
    success = test_original_error_scenario()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ All tests passed! Original error scenario is fixed.")
        print("The implementation correctly handles:")
        print("  - Adding ReplicaId to existing labelnames")
        print("  - Providing default values for all tag keys")
        print("  - Preserving ReplicaId in labels() method calls")
        print("  - Working with various labelname configurations")
    else:
        print("✗ Some tests failed.")
    print("=" * 60)
