#!/usr/bin/env python3
"""
Test script to verify that ReplicaId is properly set in Ray metrics.
"""

import sys
import os

# Add the vllm directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

def test_replica_id_function():
    """Test the _get_replica_id function directly."""
    print("Testing _get_replica_id function...")
    
    # Test v1 implementation
    try:
        from vllm.v1.metrics.ray_wrappers import _get_replica_id
        replica_id = _get_replica_id()
        print(f"V1 ReplicaId: {replica_id}")
        assert replica_id.startswith("replica-"), f"Expected replica- prefix, got: {replica_id}"
        print("✓ V1 _get_replica_id works correctly")
    except Exception as e:
        print(f"✗ V1 _get_replica_id failed: {e}")
    
    # Test legacy implementation
    try:
        from vllm.engine.metrics import _get_replica_id
        replica_id = _get_replica_id()
        print(f"Legacy ReplicaId: {replica_id}")
        assert replica_id.startswith("replica-"), f"Expected replica- prefix, got: {replica_id}"
        print("✓ Legacy _get_replica_id works correctly")
    except Exception as e:
        print(f"✗ Legacy _get_replica_id failed: {e}")


def test_ray_wrappers():
    """Test that Ray wrapper classes can be instantiated and have ReplicaId set."""
    print("\nTesting Ray wrapper classes...")

    # Test if Ray is available
    try:
        import ray
        from ray.util import metrics as ray_metrics
        print("✓ Ray is available")

        # Initialize Ray if not already initialized
        if not ray.is_initialized():
            ray.init(local_mode=True, ignore_reinit_error=True)
            print("✓ Ray initialized in local mode")

    except ImportError:
        print("✗ Ray is not available, skipping Ray wrapper tests")
        return

    # Test v1 wrappers
    try:
        from vllm.v1.metrics.ray_wrappers import RayGaugeWrapper, RayCounterWrapper, RayHistogramWrapper

        # Test RayGaugeWrapper
        gauge = RayGaugeWrapper("test_gauge", "Test gauge")
        print("✓ V1 RayGaugeWrapper instantiated successfully")

        # Check if ReplicaId is set in default tags
        if hasattr(gauge.metric, '_default_tags') and gauge.metric._default_tags:
            if 'ReplicaId' in gauge.metric._default_tags:
                print(f"✓ V1 RayGaugeWrapper has ReplicaId: {gauge.metric._default_tags['ReplicaId']}")
            else:
                print("✗ V1 RayGaugeWrapper missing ReplicaId in default tags")

        # Test RayCounterWrapper
        counter = RayCounterWrapper("test_counter", "Test counter")
        print("✓ V1 RayCounterWrapper instantiated successfully")

        # Test RayHistogramWrapper
        histogram = RayHistogramWrapper("test_histogram", "Test histogram")
        print("✓ V1 RayHistogramWrapper instantiated successfully")

    except Exception as e:
        print(f"✗ V1 Ray wrappers failed: {e}")
        import traceback
        traceback.print_exc()

    # Test legacy wrappers
    try:
        from vllm.engine.metrics import _RayGaugeWrapper, _RayCounterWrapper, _RayHistogramWrapper

        # Test _RayGaugeWrapper
        gauge = _RayGaugeWrapper("test_gauge_legacy", "Test gauge legacy")
        print("✓ Legacy _RayGaugeWrapper instantiated successfully")

        # Check if ReplicaId is set in default tags
        if hasattr(gauge._gauge, '_default_tags') and gauge._gauge._default_tags:
            if 'ReplicaId' in gauge._gauge._default_tags:
                print(f"✓ Legacy _RayGaugeWrapper has ReplicaId: {gauge._gauge._default_tags['ReplicaId']}")
            else:
                print("✗ Legacy _RayGaugeWrapper missing ReplicaId in default tags")

        # Test _RayCounterWrapper
        counter = _RayCounterWrapper("test_counter_legacy", "Test counter legacy")
        print("✓ Legacy _RayCounterWrapper instantiated successfully")

        # Test _RayHistogramWrapper
        histogram = _RayHistogramWrapper("test_histogram_legacy", "Test histogram legacy")
        print("✓ Legacy _RayHistogramWrapper instantiated successfully")

    except Exception as e:
        print(f"✗ Legacy Ray wrappers failed: {e}")
        import traceback
        traceback.print_exc()

    # Shutdown Ray
    try:
        ray.shutdown()
        print("✓ Ray shutdown successfully")
    except Exception as e:
        print(f"Warning: Ray shutdown failed: {e}")


if __name__ == "__main__":
    print("Testing ReplicaId implementation for Ray metrics...")
    print("=" * 60)
    
    test_replica_id_function()
    test_ray_wrappers()
    
    print("\n" + "=" * 60)
    print("Test completed!")
