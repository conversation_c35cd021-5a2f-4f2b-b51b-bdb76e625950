# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project
import time
from typing import Optional, Union

from vllm.v1.metrics.loggers import PrometheusStatLogger
from vllm.v1.spec_decode.metrics import SpecDecoding<PERSON>rom

try:
    from ray.util import metrics as ray_metrics
    from ray.util.metrics import Metric
    import ray
except ImportError:
    ray_metrics = None
    ray = None


def _get_replica_id() -> str:
    """
    Get a concise replica ID instead of the verbose WorkerId.

    This function attempts to get a meaningful identifier for the current
    Ray worker that is more concise than the default WorkerId.

    Returns:
        str: A replica ID in the format "replica-{rank}" where rank is the
             worker's rank, or "replica-unknown" if rank cannot be determined.
    """
    try:
        # Try to get the worker rank from distributed parallel state
        from vllm.distributed.parallel_state import get_tensor_model_parallel_rank
        rank = get_tensor_model_parallel_rank()
        return f"replica-{rank}"
    except Exception:
        try:
            # Fallback: try to get a shorter identifier from <PERSON>'s runtime context
            if ray is not None:
                worker_id = ray.get_runtime_context().get_worker_id()
                # Extract last 8 characters of worker ID for brevity
                short_id = worker_id[-8:] if len(worker_id) > 8 else worker_id
                return f"replica-{short_id}"
        except Exception:
            pass

    # Final fallback
    return "replica-unknown"


class RayPrometheusMetric:

    def __init__(self):
        if ray_metrics is None:
            raise ImportError(
                "RayPrometheusMetric requires Ray to be installed.")

        self.metric: Metric = None

    def labels(self, *labels, **labelskwargs):
        # Always preserve ReplicaId in merged tags
        merged_tags = {"ReplicaId": _get_replica_id()}

        # Ensure all tag keys have values by providing empty string defaults
        for tag_key in self.metric._tag_keys:
            if tag_key != "ReplicaId":
                merged_tags[tag_key] = ""

        if labelskwargs:
            for k, v in labelskwargs.items():
                if not isinstance(v, str):
                    labelskwargs[k] = str(v)
            merged_tags.update(labelskwargs)

        if labels:
            # If "ReplicaId" is in tag_keys but not provided in labels, add a placeholder
            tag_keys = list(self.metric._tag_keys)
            if "ReplicaId" in tag_keys and len(labels) == len(tag_keys) - 1:
                # Insert a placeholder for ReplicaId at the correct position
                replica_idx = tag_keys.index("ReplicaId")
                labels_list = list(labels)
                labels_list.insert(replica_idx, _get_replica_id())
                labels = tuple(labels_list)
            if len(labels) != len(self.metric._tag_keys):
                raise ValueError(
                    "Number of labels must match the number of tag keys. "
                    f"Expected {len(self.metric._tag_keys)}, got {len(labels)}"
                )

            label_dict = dict(zip(tag_keys, labels))
            merged_tags.update(label_dict)

        # Ensure ReplicaId is always set to our value (in case it was overridden)
        merged_tags["ReplicaId"] = _get_replica_id()

        self.metric.set_default_tags(merged_tags)

        return self


class RayGaugeWrapper(RayPrometheusMetric):
    """Wraps around ray.util.metrics.Gauge to provide same API as
    prometheus_client.Gauge"""

    def __init__(self,
                 name: str,
                 documentation: Optional[str] = "",
                 labelnames: Optional[list[str]] = None,
                 multiprocess_mode: Optional[str] = ""):

        # All Ray metrics are keyed by WorkerId by default, but we override this
        # with ReplicaId for more concise identification. Multiprocess modes like
        # "mostrecent", "all", "sum" do not apply. This logic can be manually
        # implemented at the observability layer (Prometheus/Grafana).
        del multiprocess_mode

        # Add ReplicaId to tag_keys to allow setting it as a default tag
        labelnames_list = list(labelnames) if labelnames else []
        if "ReplicaId" not in labelnames_list:
            labelnames_list.append("ReplicaId")
        labelnames_tuple = tuple(labelnames_list)

        self.metric = ray_metrics.Gauge(name=name,
                                        description=documentation,
                                        tag_keys=labelnames_tuple)

        # Set ReplicaId as a default tag to provide a more concise identifier
        # than the verbose WorkerId that Ray automatically adds
        self.metric.set_default_tags({"ReplicaId": _get_replica_id()})

    def set(self, value: Union[int, float]):
        return self.metric.set(value)

    def set_to_current_time(self):
        # ray metrics doesn't have set_to_current time, https://docs.ray.io/en/latest/_modules/ray/util/metrics.html
        return self.metric.set(time.time())


class RayCounterWrapper(RayPrometheusMetric):
    """Wraps around ray.util.metrics.Counter to provide same API as
    prometheus_client.Counter"""

    def __init__(self,
                 name: str,
                 documentation: Optional[str] = "",
                 labelnames: Optional[list[str]] = None):
        # Add ReplicaId to tag_keys to allow setting it as a default tag
        labelnames_list = list(labelnames) if labelnames else []
        if "ReplicaId" not in labelnames_list:
            labelnames_list.append("ReplicaId")
        labelnames_tuple = tuple(labelnames_list)

        self.metric = ray_metrics.Counter(name=name,
                                          description=documentation,
                                          tag_keys=labelnames_tuple)

        # Set ReplicaId as a default tag to provide a more concise identifier
        # than the verbose WorkerId that Ray automatically adds
        self.metric.set_default_tags({"ReplicaId": _get_replica_id()})

    def inc(self, value: Union[int, float] = 1.0):
        if value == 0:
            return
        return self.metric.inc(value)


class RayHistogramWrapper(RayPrometheusMetric):
    """Wraps around ray.util.metrics.Histogram to provide same API as
    prometheus_client.Histogram"""

    def __init__(self,
                 name: str,
                 documentation: Optional[str] = "",
                 labelnames: Optional[list[str]] = None,
                 buckets: Optional[list[float]] = None):
        # Add ReplicaId to tag_keys to allow setting it as a default tag
        labelnames_list = list(labelnames) if labelnames else []
        if "ReplicaId" not in labelnames_list:
            labelnames_list.append("ReplicaId")
        labelnames_tuple = tuple(labelnames_list)

        boundaries = buckets if buckets else []
        self.metric = ray_metrics.Histogram(name=name,
                                            description=documentation,
                                            tag_keys=labelnames_tuple,
                                            boundaries=boundaries)

        # Set ReplicaId as a default tag to provide a more concise identifier
        # than the verbose WorkerId that Ray automatically adds
        self.metric.set_default_tags({"ReplicaId": _get_replica_id()})

    def observe(self, value: Union[int, float]):
        return self.metric.observe(value)


class RaySpecDecodingProm(SpecDecodingProm):
    """
    RaySpecDecodingProm is used by RayMetrics to log to Ray metrics.
    Provides the same metrics as SpecDecodingProm but uses Ray's
    util.metrics library.
    """

    _counter_cls = RayCounterWrapper


class RayPrometheusStatLogger(PrometheusStatLogger):
    """RayPrometheusStatLogger uses Ray metrics instead."""

    _gauge_cls = RayGaugeWrapper
    _counter_cls = RayCounterWrapper
    _histogram_cls = RayHistogramWrapper
    _spec_decoding_cls = RaySpecDecodingProm

    @staticmethod
    def _unregister_vllm_metrics():
        # No-op on purpose
        pass
